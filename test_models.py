#!/usr/bin/env python3
"""
Test script for Healthcare AI models
Tests the health prediction models with sample data
"""

import sys
import os
import json
from datetime import datetime

# Add the models directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.health_predictor import HealthData, HealthPredictor


def test_health_predictor():
    """Test the health predictor with sample data"""

    print("🏥 Healthcare AI - Model Testing")
    print("=" * 50)

    # Initialize the predictor
    predictor = HealthPredictor()
    print(f"Model Version: {predictor.model_version}")
    print(f"Last Updated: {predictor.last_updated}")
    print()

    # Test cases
    test_cases = [
        {
            "name": "Low Risk Patient",
            "data": HealthData(
                age=25,
                gender="female",
                height=165,
                weight=60,
                systolic_bp=110,
                diastolic_bp=70,
                cholesterol=180,
                glucose=90,
                smoking=False,
                family_history=False,
                exercise_hours_per_week=5,
                stress_level=3
            )
        },
        {
            "name": "Moderate Risk Patient",
            "data": HealthData(
                age=45,
                gender="male",
                height=175,
                weight=80,
                systolic_bp=130,
                diastolic_bp=85,
                cholesterol=220,
                glucose=110,
                smoking=False,
                family_history=True,
                exercise_hours_per_week=2,
                stress_level=6
            )
        },
        {
            "name": "High Risk Patient",
            "data": HealthData(
                age=60,
                gender="male",
                height=170,
                weight=95,
                systolic_bp=150,
                diastolic_bp=95,
                cholesterol=280,
                glucose=140,
                smoking=True,
                family_history=True,
                exercise_hours_per_week=0.5,
                stress_level=8
            )
        }
    ]

    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['name']}")
        print("-" * 30)

        data = test_case['data']

        # Calculate BMI
        bmi = predictor.calculate_bmi(data.height, data.weight)
        print(f"BMI: {bmi:.1f}")

        # Test diabetes prediction
        diabetes_result = predictor.predict_diabetes_risk(data)
        print(f"Diabetes Risk: {diabetes_result.risk_score:.1f}/100 ({diabetes_result.risk_level})")

        # Test heart disease prediction
        heart_result = predictor.predict_heart_disease_risk(data)
        print(f"Heart Disease Risk: {heart_result.risk_score:.1f}/100 ({heart_result.risk_level})")

        # Test general health prediction
        general_result = predictor.predict_general_health_risk(data)
        print(f"General Health Risk: {general_result.risk_score:.1f}/100 ({general_result.risk_level})")

        # Test comprehensive assessment
        comprehensive = predictor.get_comprehensive_assessment(data)
        print(f"Comprehensive Assessment: {len(comprehensive)} predictions generated")

        print(f"Top Recommendations:")
        for rec in diabetes_result.recommendations[:2]:
            print(f"  • {rec}")

        print()

    print("✅ All tests completed successfully!")
    return True


def test_api_compatibility():
    """Test API data format compatibility"""

    print("🔗 Testing API Compatibility")
    print("=" * 30)

    # Sample API request data
    api_data = {
        "age": 45,
        "gender": "male",
        "height": 175.0,
        "weight": 80.0,
        "systolic_bp": 130,
        "diastolic_bp": 85,
        "cholesterol": 220.0,
        "glucose": 110.0,
        "smoking": False,
        "family_history": True,
        "exercise_hours_per_week": 2.0,
        "stress_level": 6
    }

    try:
        # Convert API data to HealthData object
        health_data = HealthData(**api_data)
        print(f"✅ API data conversion successful")

        # Test prediction
        predictor = HealthPredictor()
        result = predictor.predict_diabetes_risk(health_data)

        # Convert result to API response format
        api_response = {
            "condition": result.condition,
            "risk_score": round(result.risk_score, 2),
            "risk_level": result.risk_level,
            "confidence": round(result.confidence, 2),
            "recommendations": result.recommendations,
            "factors": {k: round(v, 2) for k, v in result.factors.items()},
            "timestamp": datetime.now().isoformat()
        }

        print(f"✅ API response format successful")
        print(f"Sample response: {json.dumps(api_response, indent=2)}")

        return True

    except Exception as e:
        print(f"❌ API compatibility test failed: {e}")
        return False


if __name__ == "__main__":
    print("Starting Healthcare AI Model Tests...")
    print()

    try:
        # Test the health predictor models
        test_health_predictor()
        print()

        # Test API compatibility
        test_api_compatibility()
        print()

        print("🎉 All tests passed! The Healthcare AI system is ready to use.")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)
