# 🏥 Healthcare AI - Health Risk Assessment System

An AI-powered healthcare application that provides personalized health risk assessments for diabetes, heart disease, and general health conditions. Built with Python and featuring a modern web interface.

## ✨ Features

- **🍯 Diabetes Risk Prediction**: Assess Type 2 diabetes risk based on clinical indicators
- **❤️ Heart Disease Risk Assessment**: Evaluate cardiovascular health risks
- **🌟 General Health Analysis**: Overall wellness and health risk evaluation
- **📊 Comprehensive Reports**: Detailed risk scores, confidence levels, and personalized recommendations
- **🎯 Personalized Recommendations**: AI-generated health advice based on individual risk factors
- **📱 Modern Web Interface**: Responsive, user-friendly web application
- **🔒 Privacy-First**: All processing happens locally - no data sent to external servers

## 🚀 Quick Start

### Prerequisites

- Python 3.7 or higher
- Web browser (Chrome, Firefox, Safari, Edge)

### Installation & Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd healthcare-ai
   ```

2. **Test the models** (optional but recommended)
   ```bash
   python test_models.py
   ```

3. **Start the server**
   ```bash
   python simple_server.py
   ```

4. **Open the application**
   - Open your web browser
   - Go to: `http://localhost:8000/frontend/index.html`
   - Fill out the health assessment form
   - Click "Analyze Health Risk" to get your results

## 📁 Project Structure

```
healthcare-ai/
├── models/
│   └── health_predictor.py    # AI prediction models
├── backend/
│   └── main.py               # FastAPI backend (optional)
├── frontend/
│   └── index.html            # Web interface
├── simple_server.py          # Simple HTTP server
├── test_models.py           # Model testing script
├── requirements.txt         # Python dependencies
├── package.json            # Project metadata
└── README.md               # This file
```

## 🧠 How It Works

### AI Models

The system uses clinically-informed mathematical models that analyze:

- **Demographics**: Age, gender
- **Physical Metrics**: Height, weight, BMI
- **Vital Signs**: Blood pressure (systolic/diastolic)
- **Lab Values**: Cholesterol, glucose levels
- **Lifestyle Factors**: Exercise, smoking, stress levels
- **Medical History**: Family history of chronic diseases

### Risk Assessment

Each condition is evaluated using weighted risk factors:

1. **Data Collection**: User inputs health information
2. **Risk Calculation**: AI models process the data using clinical guidelines
3. **Score Generation**: Risk scores (0-100) and levels (Low/Moderate/High/Very High)
4. **Recommendations**: Personalized health advice based on risk factors
5. **Comprehensive Report**: Combined assessment with priority actions

## 🎯 Health Conditions Assessed

### Diabetes Risk (Type 2)
- Focuses on metabolic indicators
- Considers BMI, glucose levels, family history
- Provides lifestyle modification recommendations

### Heart Disease Risk
- Evaluates cardiovascular risk factors
- Analyzes blood pressure, cholesterol, smoking status
- Emphasizes prevention strategies

### General Health Risk
- Overall wellness assessment
- Combines multiple health indicators
- Provides holistic health recommendations

## 🌐 API Endpoints

The system provides RESTful API endpoints:

- `GET /` - API information and documentation
- `GET /health` - Server health check
- `POST /predict/diabetes` - Diabetes risk prediction
- `POST /predict/heart-disease` - Heart disease risk prediction
- `POST /predict/general-health` - General health assessment
- `POST /predict/comprehensive` - Complete health evaluation

### Example API Request

```json
{
  "age": 45,
  "gender": "male",
  "height": 175.0,
  "weight": 80.0,
  "systolic_bp": 130,
  "diastolic_bp": 85,
  "cholesterol": 220.0,
  "glucose": 110.0,
  "smoking": false,
  "family_history": true,
  "exercise_hours_per_week": 2.0,
  "stress_level": 6
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_models.py
```

This will test:
- Model accuracy with different risk profiles
- API data format compatibility
- Response format validation
- Error handling

## 🔧 Configuration

### Server Options

```bash
# Run on different port
python simple_server.py --port 8080

# Run with FastAPI (if dependencies available)
cd backend && python -m uvicorn main:app --reload --port 8000
```

### Customization

- **Risk Thresholds**: Modify risk level boundaries in `health_predictor.py`
- **Recommendations**: Update recommendation logic for different conditions
- **UI Styling**: Customize the frontend appearance in `frontend/index.html`

## 📊 Sample Results

The system provides detailed assessments like:

```
📈 Overall Health Summary: Moderate Risk
🍯 Diabetes Risk: 64.4/100 (High)
❤️ Heart Disease Risk: 76.4/100 (Very High)
🌟 General Health: 60.7/100 (High)

🎯 Priority Recommendations:
• Maintain a healthy weight through diet and exercise
• Increase physical activity to at least 150 minutes per week
• Monitor blood glucose levels regularly
• Quit smoking immediately
• Consult a doctor about blood pressure management
```

## ⚠️ Important Disclaimers

- **Not Medical Advice**: This tool is for educational purposes only
- **Consult Healthcare Providers**: Always seek professional medical advice
- **Emergency Situations**: Call emergency services for urgent health concerns
- **Accuracy Limitations**: AI predictions are estimates, not diagnoses

## 🤝 Contributing

Contributions are welcome! Areas for improvement:

- Enhanced ML models with real clinical data
- Additional health conditions
- Mobile app development
- Integration with wearable devices
- Multi-language support

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Clinical guidelines from major health organizations
- Open-source Python ecosystem
- Healthcare AI research community

---

**Built with ❤️ for better health outcomes**
