"""
Healthcare AI Backend API
FastAPI-based REST API for health predictions and assessments
"""

import sys
import os
from typing import Dict, List, Optional
from datetime import datetime
import json

# Add the parent directory to the path to import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from fastapi import Fast<PERSON>I, HTTPException, status
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field, validator
except ImportError:
    # Fallback for systems without FastAPI
    print("FastAPI not available. Using basic HTTP server.")
    import http.server
    import socketserver
    import urllib.parse

from models.health_predictor import HealthData, HealthPredictor, PredictionResult

# Initialize FastAPI app
app = FastAPI(
    title="Healthcare AI API",
    description="AI-Powered Health Risk Assessment and Prediction System",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the health predictor
predictor = HealthPredictor()


# Pydantic models for API
class HealthDataRequest(BaseModel):
    """Request model for health data input"""
    age: int = Field(..., ge=1, le=120, description="Age in years")
    gender: str = Field(..., regex="^(male|female)$", description="Gender: male or female")
    height: float = Field(..., ge=50, le=250, description="Height in centimeters")
    weight: float = Field(..., ge=20, le=300, description="Weight in kilograms")
    systolic_bp: int = Field(..., ge=70, le=250, description="Systolic blood pressure")
    diastolic_bp: int = Field(..., ge=40, le=150, description="Diastolic blood pressure")
    cholesterol: float = Field(..., ge=100, le=500, description="Cholesterol level in mg/dL")
    glucose: float = Field(..., ge=50, le=400, description="Glucose level in mg/dL")
    smoking: bool = Field(..., description="Smoking status")
    family_history: bool = Field(..., description="Family history of chronic diseases")
    exercise_hours_per_week: float = Field(..., ge=0, le=50, description="Exercise hours per week")
    stress_level: int = Field(..., ge=1, le=10, description="Stress level on 1-10 scale")

    @validator('gender')
    def validate_gender(cls, v):
        return v.lower()


class PredictionResponse(BaseModel):
    """Response model for health predictions"""
    condition: str
    risk_score: float
    risk_level: str
    confidence: float
    recommendations: List[str]
    factors: Dict[str, float]
    timestamp: str


class ComprehensiveAssessmentResponse(BaseModel):
    """Response model for comprehensive health assessment"""
    diabetes: PredictionResponse
    heart_disease: PredictionResponse
    general_health: PredictionResponse
    overall_summary: Dict[str, any]
    timestamp: str


# API Routes
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Healthcare AI API",
        "version": "1.0.0",
        "description": "AI-Powered Health Risk Assessment System",
        "endpoints": {
            "/predict/diabetes": "Diabetes risk prediction",
            "/predict/heart-disease": "Heart disease risk prediction",
            "/predict/general-health": "General health risk assessment",
            "/predict/comprehensive": "Comprehensive health assessment",
            "/health": "API health check"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "model_version": predictor.model_version
    }


def convert_prediction_to_response(prediction: PredictionResult) -> PredictionResponse:
    """Convert PredictionResult to API response format"""
    return PredictionResponse(
        condition=prediction.condition,
        risk_score=round(prediction.risk_score, 2),
        risk_level=prediction.risk_level,
        confidence=round(prediction.confidence, 2),
        recommendations=prediction.recommendations,
        factors={k: round(v, 2) for k, v in prediction.factors.items()},
        timestamp=datetime.now().isoformat()
    )


@app.post("/predict/diabetes", response_model=PredictionResponse)
async def predict_diabetes(health_data: HealthDataRequest):
    """Predict diabetes risk based on health data"""
    try:
        # Convert request to HealthData
        data = HealthData(**health_data.dict())

        # Get prediction
        prediction = predictor.predict_diabetes_risk(data)

        # Convert to response format
        return convert_prediction_to_response(prediction)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}"
        )


@app.post("/predict/heart-disease", response_model=PredictionResponse)
async def predict_heart_disease(health_data: HealthDataRequest):
    """Predict heart disease risk based on health data"""
    try:
        # Convert request to HealthData
        data = HealthData(**health_data.dict())

        # Get prediction
        prediction = predictor.predict_heart_disease_risk(data)

        # Convert to response format
        return convert_prediction_to_response(prediction)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}"
        )


@app.post("/predict/general-health", response_model=PredictionResponse)
async def predict_general_health(health_data: HealthDataRequest):
    """Predict general health risk based on health data"""
    try:
        # Convert request to HealthData
        data = HealthData(**health_data.dict())

        # Get prediction
        prediction = predictor.predict_general_health_risk(data)

        # Convert to response format
        return convert_prediction_to_response(prediction)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}"
        )


@app.post("/predict/comprehensive", response_model=ComprehensiveAssessmentResponse)
async def predict_comprehensive(health_data: HealthDataRequest):
    """Get comprehensive health assessment for all conditions"""
    try:
        # Convert request to HealthData
        data = HealthData(**health_data.dict())

        # Get comprehensive assessment
        assessments = predictor.get_comprehensive_assessment(data)

        # Convert predictions to response format
        diabetes_response = convert_prediction_to_response(assessments['diabetes'])
        heart_disease_response = convert_prediction_to_response(assessments['heart_disease'])
        general_health_response = convert_prediction_to_response(assessments['general_health'])

        # Calculate overall summary
        avg_risk_score = (
            assessments['diabetes'].risk_score +
            assessments['heart_disease'].risk_score +
            assessments['general_health'].risk_score
        ) / 3

        highest_risk = max(
            assessments['diabetes'].risk_score,
            assessments['heart_disease'].risk_score,
            assessments['general_health'].risk_score
        )

        # Determine overall risk level
        if highest_risk < 25:
            overall_risk_level = "Low"
        elif highest_risk < 50:
            overall_risk_level = "Moderate"
        elif highest_risk < 75:
            overall_risk_level = "High"
        else:
            overall_risk_level = "Very High"

        # Collect all unique recommendations
        all_recommendations = set()
        for assessment in assessments.values():
            all_recommendations.update(assessment.recommendations)

        overall_summary = {
            "average_risk_score": round(avg_risk_score, 2),
            "highest_risk_score": round(highest_risk, 2),
            "overall_risk_level": overall_risk_level,
            "priority_recommendations": list(all_recommendations)[:5],  # Top 5 recommendations
            "bmi": round(predictor.calculate_bmi(data.height, data.weight), 1)
        }

        return ComprehensiveAssessmentResponse(
            diabetes=diabetes_response,
            heart_disease=heart_disease_response,
            general_health=general_health_response,
            overall_summary=overall_summary,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Comprehensive assessment failed: {str(e)}"
        )


# Run the application
if __name__ == "__main__":
    try:
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8000)
    except ImportError:
        print("Uvicorn not available. Please install FastAPI and Uvicorn to run the server.")
        print("For now, you can test the models directly by importing them.")
