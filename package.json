{"name": "healthcare-ai-app", "version": "1.0.0", "description": "AI-Powered Healthcare Diagnosis Application", "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start-backend": "cd backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000", "install-frontend": "cd frontend && npm install", "setup": "pip install -r requirements.txt && npm run install-frontend"}, "keywords": ["healthcare", "ai", "machine-learning", "diagnosis"], "author": "Healthcare AI Team", "license": "MIT"}