<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare AI - Health Risk Assessment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .results-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .prediction-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #4facfe;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .prediction-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .risk-score {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .risk-low { color: #28a745; }
        .risk-moderate { color: #ffc107; }
        .risk-high { color: #fd7e14; }
        .risk-very-high { color: #dc3545; }

        .recommendations {
            margin-top: 15px;
        }

        .recommendations h4 {
            color: #555;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            background: #e3f2fd;
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Healthcare AI</h1>
            <p>AI-Powered Health Risk Assessment & Prediction System</p>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h2>📋 Health Information</h2>
                <form id="healthForm">
                    <div class="form-group">
                        <label for="age">Age (years)</label>
                        <input type="number" id="age" name="age" min="1" max="120" required>
                    </div>

                    <div class="form-group">
                        <label for="gender">Gender</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="height">Height (cm)</label>
                        <input type="number" id="height" name="height" min="50" max="250" step="0.1" required>
                    </div>

                    <div class="form-group">
                        <label for="weight">Weight (kg)</label>
                        <input type="number" id="weight" name="weight" min="20" max="300" step="0.1" required>
                    </div>

                    <div class="form-group">
                        <label for="systolic_bp">Systolic Blood Pressure</label>
                        <input type="number" id="systolic_bp" name="systolic_bp" min="70" max="250" required>
                    </div>

                    <div class="form-group">
                        <label for="diastolic_bp">Diastolic Blood Pressure</label>
                        <input type="number" id="diastolic_bp" name="diastolic_bp" min="40" max="150" required>
                    </div>

                    <div class="form-group">
                        <label for="cholesterol">Cholesterol (mg/dL)</label>
                        <input type="number" id="cholesterol" name="cholesterol" min="100" max="500" step="0.1" required>
                    </div>

                    <div class="form-group">
                        <label for="glucose">Glucose (mg/dL)</label>
                        <input type="number" id="glucose" name="glucose" min="50" max="400" step="0.1" required>
                    </div>

                    <div class="form-group">
                        <label for="exercise_hours_per_week">Exercise Hours per Week</label>
                        <input type="number" id="exercise_hours_per_week" name="exercise_hours_per_week" min="0" max="50" step="0.5" required>
                    </div>

                    <div class="form-group">
                        <label for="stress_level">Stress Level (1-10)</label>
                        <input type="number" id="stress_level" name="stress_level" min="1" max="10" required>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="smoking" name="smoking">
                            <label for="smoking">Current Smoker</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="family_history" name="family_history">
                            <label for="family_history">Family History of Chronic Diseases</label>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="submitBtn">
                        🔍 Analyze Health Risk
                    </button>
                </form>
            </div>

            <div class="results-section">
                <h2>📊 Health Assessment Results</h2>
                <div id="results">
                    <div class="loading" style="display: none;" id="loading">
                        <p>🔄 Analyzing your health data...</p>
                    </div>
                    <div id="resultsContent">
                        <p style="text-align: center; color: #666; padding: 40px;">
                            Fill out the form and click "Analyze Health Risk" to get your personalized health assessment.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:8000';

        // DOM Elements
        const form = document.getElementById('healthForm');
        const submitBtn = document.getElementById('submitBtn');
        const loading = document.getElementById('loading');
        const resultsContent = document.getElementById('resultsContent');

        // Form submission handler
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 Analyzing...';
            loading.style.display = 'block';
            resultsContent.innerHTML = '';

            try {
                // Collect form data
                const formData = new FormData(form);
                const healthData = {
                    age: parseInt(formData.get('age')),
                    gender: formData.get('gender'),
                    height: parseFloat(formData.get('height')),
                    weight: parseFloat(formData.get('weight')),
                    systolic_bp: parseInt(formData.get('systolic_bp')),
                    diastolic_bp: parseInt(formData.get('diastolic_bp')),
                    cholesterol: parseFloat(formData.get('cholesterol')),
                    glucose: parseFloat(formData.get('glucose')),
                    smoking: formData.get('smoking') === 'on',
                    family_history: formData.get('family_history') === 'on',
                    exercise_hours_per_week: parseFloat(formData.get('exercise_hours_per_week')),
                    stress_level: parseInt(formData.get('stress_level'))
                };

                // Make API call for comprehensive assessment
                const response = await fetch(`${API_BASE_URL}/predict/comprehensive`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(healthData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const results = await response.json();
                displayResults(results);

            } catch (error) {
                console.error('Error:', error);
                displayError(error.message);
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.textContent = '🔍 Analyze Health Risk';
                loading.style.display = 'none';
            }
        });

        // Display results function
        function displayResults(results) {
            const { diabetes, heart_disease, general_health, overall_summary } = results;

            resultsContent.innerHTML = `
                <div class="prediction-card">
                    <h3>📈 Overall Health Summary</h3>
                    <div class="risk-score ${getRiskClass(overall_summary.overall_risk_level)}">
                        ${overall_summary.overall_risk_level} Risk
                    </div>
                    <p><strong>Average Risk Score:</strong> ${overall_summary.average_risk_score}/100</p>
                    <p><strong>BMI:</strong> ${overall_summary.bmi} ${getBMICategory(overall_summary.bmi)}</p>
                </div>

                <div class="prediction-card">
                    <h3>🍯 Diabetes Risk</h3>
                    <div class="risk-score ${getRiskClass(diabetes.risk_level)}">
                        ${diabetes.risk_score}/100 - ${diabetes.risk_level}
                    </div>
                    <p><strong>Confidence:</strong> ${diabetes.confidence}%</p>
                    <div class="recommendations">
                        <h4>Recommendations:</h4>
                        <ul>
                            ${diabetes.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="prediction-card">
                    <h3>❤️ Heart Disease Risk</h3>
                    <div class="risk-score ${getRiskClass(heart_disease.risk_level)}">
                        ${heart_disease.risk_score}/100 - ${heart_disease.risk_level}
                    </div>
                    <p><strong>Confidence:</strong> ${heart_disease.confidence}%</p>
                    <div class="recommendations">
                        <h4>Recommendations:</h4>
                        <ul>
                            ${heart_disease.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="prediction-card">
                    <h3>🌟 General Health</h3>
                    <div class="risk-score ${getRiskClass(general_health.risk_level)}">
                        ${general_health.risk_score}/100 - ${general_health.risk_level}
                    </div>
                    <p><strong>Confidence:</strong> ${general_health.confidence}%</p>
                    <div class="recommendations">
                        <h4>Recommendations:</h4>
                        <ul>
                            ${general_health.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="prediction-card">
                    <h3>🎯 Priority Actions</h3>
                    <div class="recommendations">
                        <ul>
                            ${overall_summary.priority_recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }

        // Display error function
        function displayError(message) {
            resultsContent.innerHTML = `
                <div class="error">
                    <h3>❌ Error</h3>
                    <p>Unable to process your health assessment: ${message}</p>
                    <p>Please check that the backend server is running on port 8000.</p>
                </div>
            `;
        }

        // Helper function to get risk level CSS class
        function getRiskClass(riskLevel) {
            switch (riskLevel.toLowerCase()) {
                case 'low': return 'risk-low';
                case 'moderate': return 'risk-moderate';
                case 'high': return 'risk-high';
                case 'very high': return 'risk-very-high';
                default: return 'risk-moderate';
            }
        }

        // Helper function to get BMI category
        function getBMICategory(bmi) {
            if (bmi < 18.5) return '(Underweight)';
            if (bmi < 25) return '(Normal)';
            if (bmi < 30) return '(Overweight)';
            return '(Obese)';
        }

        // Add some sample data for testing
        function fillSampleData() {
            document.getElementById('age').value = 45;
            document.getElementById('gender').value = 'male';
            document.getElementById('height').value = 175;
            document.getElementById('weight').value = 80;
            document.getElementById('systolic_bp').value = 130;
            document.getElementById('diastolic_bp').value = 85;
            document.getElementById('cholesterol').value = 220;
            document.getElementById('glucose').value = 110;
            document.getElementById('exercise_hours_per_week').value = 2;
            document.getElementById('stress_level').value = 6;
            document.getElementById('smoking').checked = false;
            document.getElementById('family_history').checked = true;
        }

        // Add a button to fill sample data (for testing)
        const sampleBtn = document.createElement('button');
        sampleBtn.textContent = '📝 Fill Sample Data';
        sampleBtn.type = 'button';
        sampleBtn.className = 'btn';
        sampleBtn.style.marginTop = '10px';
        sampleBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        sampleBtn.onclick = fillSampleData;
        form.appendChild(sampleBtn);
    </script>
</body>
</html>
