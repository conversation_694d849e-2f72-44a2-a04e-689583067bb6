#!/usr/bin/env python3
"""
Simple HTTP Server for Healthcare AI
Fallback server that works without external dependencies
"""

import http.server
import socketserver
import json
import urllib.parse
import sys
import os
from datetime import datetime

# Add the models directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.health_predictor import HealthData, HealthPredictor


class HealthcareHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP handler for healthcare AI API"""

    def __init__(self, *args, **kwargs):
        self.predictor = HealthPredictor()
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.send_api_info()
        elif self.path == '/health':
            self.send_health_check()
        elif self.path.startswith('/frontend'):
            # Serve frontend files
            self.path = self.path.replace('/frontend', '/frontend')
            super().do_GET()
        else:
            self.send_error(404, "Endpoint not found")

    def do_POST(self):
        """Handle POST requests"""
        try:
            # Parse the request path
            if self.path == '/predict/diabetes':
                self.handle_diabetes_prediction()
            elif self.path == '/predict/heart-disease':
                self.handle_heart_disease_prediction()
            elif self.path == '/predict/general-health':
                self.handle_general_health_prediction()
            elif self.path == '/predict/comprehensive':
                self.handle_comprehensive_prediction()
            else:
                self.send_error(404, "Endpoint not found")
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")

    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def send_json_response(self, data, status_code=200):
        """Send JSON response with CORS headers"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_cors_headers()
        self.end_headers()

        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))

    def get_request_data(self):
        """Parse JSON data from request body"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        return json.loads(post_data.decode('utf-8'))

    def send_api_info(self):
        """Send API information"""
        info = {
            "message": "Healthcare AI API",
            "version": "1.0.0",
            "description": "AI-Powered Health Risk Assessment System",
            "endpoints": {
                "/predict/diabetes": "Diabetes risk prediction",
                "/predict/heart-disease": "Heart disease risk prediction",
                "/predict/general-health": "General health risk assessment",
                "/predict/comprehensive": "Comprehensive health assessment",
                "/health": "API health check"
            }
        }
        self.send_json_response(info)

    def send_health_check(self):
        """Send health check response"""
        health = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "model_version": self.predictor.model_version
        }
        self.send_json_response(health)

    def convert_prediction_to_response(self, prediction):
        """Convert PredictionResult to API response format"""
        return {
            "condition": prediction.condition,
            "risk_score": round(prediction.risk_score, 2),
            "risk_level": prediction.risk_level,
            "confidence": round(prediction.confidence, 2),
            "recommendations": prediction.recommendations,
            "factors": {k: round(v, 2) for k, v in prediction.factors.items()},
            "timestamp": datetime.now().isoformat()
        }

    def handle_diabetes_prediction(self):
        """Handle diabetes prediction request"""
        try:
            data = self.get_request_data()
            health_data = HealthData(**data)
            prediction = self.predictor.predict_diabetes_risk(health_data)
            response = self.convert_prediction_to_response(prediction)
            self.send_json_response(response)
        except Exception as e:
            self.send_json_response({"error": f"Prediction failed: {str(e)}"}, 500)

    def handle_heart_disease_prediction(self):
        """Handle heart disease prediction request"""
        try:
            data = self.get_request_data()
            health_data = HealthData(**data)
            prediction = self.predictor.predict_heart_disease_risk(health_data)
            response = self.convert_prediction_to_response(prediction)
            self.send_json_response(response)
        except Exception as e:
            self.send_json_response({"error": f"Prediction failed: {str(e)}"}, 500)

    def handle_general_health_prediction(self):
        """Handle general health prediction request"""
        try:
            data = self.get_request_data()
            health_data = HealthData(**data)
            prediction = self.predictor.predict_general_health_risk(health_data)
            response = self.convert_prediction_to_response(prediction)
            self.send_json_response(response)
        except Exception as e:
            self.send_json_response({"error": f"Prediction failed: {str(e)}"}, 500)

    def handle_comprehensive_prediction(self):
        """Handle comprehensive health assessment request"""
        try:
            data = self.get_request_data()
            health_data = HealthData(**data)

            # Get comprehensive assessment
            assessments = self.predictor.get_comprehensive_assessment(health_data)

            # Convert predictions to response format
            diabetes_response = self.convert_prediction_to_response(assessments['diabetes'])
            heart_disease_response = self.convert_prediction_to_response(assessments['heart_disease'])
            general_health_response = self.convert_prediction_to_response(assessments['general_health'])

            # Calculate overall summary
            avg_risk_score = (
                assessments['diabetes'].risk_score +
                assessments['heart_disease'].risk_score +
                assessments['general_health'].risk_score
            ) / 3

            highest_risk = max(
                assessments['diabetes'].risk_score,
                assessments['heart_disease'].risk_score,
                assessments['general_health'].risk_score
            )

            # Determine overall risk level
            if highest_risk < 25:
                overall_risk_level = "Low"
            elif highest_risk < 50:
                overall_risk_level = "Moderate"
            elif highest_risk < 75:
                overall_risk_level = "High"
            else:
                overall_risk_level = "Very High"

            # Collect all unique recommendations
            all_recommendations = set()
            for assessment in assessments.values():
                all_recommendations.update(assessment.recommendations)

            overall_summary = {
                "average_risk_score": round(avg_risk_score, 2),
                "highest_risk_score": round(highest_risk, 2),
                "overall_risk_level": overall_risk_level,
                "priority_recommendations": list(all_recommendations)[:5],  # Top 5 recommendations
                "bmi": round(self.predictor.calculate_bmi(health_data.height, health_data.weight), 1)
            }

            response = {
                "diabetes": diabetes_response,
                "heart_disease": heart_disease_response,
                "general_health": general_health_response,
                "overall_summary": overall_summary,
                "timestamp": datetime.now().isoformat()
            }

            self.send_json_response(response)

        except Exception as e:
            self.send_json_response({"error": f"Comprehensive assessment failed: {str(e)}"}, 500)


def run_server(port=8000):
    """Run the healthcare AI server"""
    try:
        with socketserver.TCPServer(("", port), HealthcareHandler) as httpd:
            print(f"🏥 Healthcare AI Server starting on port {port}")
            print(f"📊 API available at: http://localhost:{port}")
            print(f"🌐 Frontend available at: http://localhost:{port}/frontend/index.html")
            print(f"📋 API documentation at: http://localhost:{port}")
            print("Press Ctrl+C to stop the server")
            print()

            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Healthcare AI Server')
    parser.add_argument('--port', type=int, default=8000, help='Port to run the server on')
    args = parser.parse_args()

    run_server(args.port)
