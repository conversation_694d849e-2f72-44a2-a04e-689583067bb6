"""
Healthcare AI Prediction Models
Simplified implementation using mathematical formulas for health risk assessment
"""

import json
import random
from typing import Dict, List, Tuple
from dataclasses import dataclass
from datetime import datetime


@dataclass
class HealthData:
    """Patient health data structure"""
    age: int
    gender: str  # 'male' or 'female'
    height: float  # in cm
    weight: float  # in kg
    systolic_bp: int  # systolic blood pressure
    diastolic_bp: int  # diastolic blood pressure
    cholesterol: float  # mg/dL
    glucose: float  # mg/dL
    smoking: bool
    family_history: bool
    exercise_hours_per_week: float
    stress_level: int  # 1-10 scale


@dataclass
class PredictionResult:
    """Health prediction result structure"""
    condition: str
    risk_score: float  # 0-100 scale
    risk_level: str  # 'Low', 'Moderate', 'High', 'Very High'
    confidence: float  # 0-100 scale
    recommendations: List[str]
    factors: Dict[str, float]  # Contributing factors


class HealthPredictor:
    """Main health prediction engine"""

    def __init__(self):
        self.model_version = "1.0.0"
        self.last_updated = datetime.now().isoformat()

    def calculate_bmi(self, height_cm: float, weight_kg: float) -> float:
        """Calculate Body Mass Index"""
        height_m = height_cm / 100
        return weight_kg / (height_m ** 2)

    def predict_diabetes_risk(self, data: HealthData) -> PredictionResult:
        """Predict diabetes risk using clinical indicators"""

        # Calculate BMI
        bmi = self.calculate_bmi(data.height, data.weight)

        # Risk factors with weights
        risk_factors = {
            'age': min((data.age - 30) * 2, 30) if data.age > 30 else 0,
            'bmi': max((bmi - 25) * 3, 0) if bmi > 25 else 0,
            'glucose': max((data.glucose - 100) * 0.5, 0) if data.glucose > 100 else 0,
            'family_history': 15 if data.family_history else 0,
            'exercise': max((3 - data.exercise_hours_per_week) * 2, 0),
            'stress': data.stress_level * 1.5
        }

        # Calculate total risk score
        base_risk = sum(risk_factors.values())
        risk_score = min(base_risk, 100)

        # Determine risk level
        if risk_score < 25:
            risk_level = "Low"
        elif risk_score < 50:
            risk_level = "Moderate"
        elif risk_score < 75:
            risk_level = "High"
        else:
            risk_level = "Very High"

        # Generate recommendations
        recommendations = []
        if bmi > 25:
            recommendations.append("Maintain a healthy weight through diet and exercise")
        if data.exercise_hours_per_week < 3:
            recommendations.append("Increase physical activity to at least 150 minutes per week")
        if data.glucose > 100:
            recommendations.append("Monitor blood glucose levels regularly")
        if data.stress_level > 6:
            recommendations.append("Practice stress management techniques")

        return PredictionResult(
            condition="Type 2 Diabetes",
            risk_score=risk_score,
            risk_level=risk_level,
            confidence=85 + random.uniform(-10, 10),
            recommendations=recommendations,
            factors=risk_factors
        )

    def predict_heart_disease_risk(self, data: HealthData) -> PredictionResult:
        """Predict heart disease risk using cardiovascular indicators"""

        # Calculate BMI
        bmi = self.calculate_bmi(data.height, data.weight)

        # Risk factors with weights
        risk_factors = {
            'age': (data.age - 20) * 1.5 if data.age > 20 else 0,
            'gender': 10 if data.gender.lower() == 'male' else 5,
            'bmi': max((bmi - 25) * 2, 0) if bmi > 25 else 0,
            'systolic_bp': max((data.systolic_bp - 120) * 0.3, 0) if data.systolic_bp > 120 else 0,
            'diastolic_bp': max((data.diastolic_bp - 80) * 0.5, 0) if data.diastolic_bp > 80 else 0,
            'cholesterol': max((data.cholesterol - 200) * 0.1, 0) if data.cholesterol > 200 else 0,
            'smoking': 20 if data.smoking else 0,
            'family_history': 12 if data.family_history else 0,
            'exercise': max((2 - data.exercise_hours_per_week) * 3, 0),
            'stress': data.stress_level * 1.2
        }

        # Calculate total risk score
        base_risk = sum(risk_factors.values())
        risk_score = min(base_risk, 100)

        # Determine risk level
        if risk_score < 20:
            risk_level = "Low"
        elif risk_score < 40:
            risk_level = "Moderate"
        elif risk_score < 70:
            risk_level = "High"
        else:
            risk_level = "Very High"

        # Generate recommendations
        recommendations = []
        if data.smoking:
            recommendations.append("Quit smoking immediately - this is the most important step")
        if data.systolic_bp > 140 or data.diastolic_bp > 90:
            recommendations.append("Consult a doctor about blood pressure management")
        if data.cholesterol > 240:
            recommendations.append("Follow a heart-healthy diet low in saturated fats")
        if data.exercise_hours_per_week < 2:
            recommendations.append("Engage in regular cardiovascular exercise")
        if bmi > 30:
            recommendations.append("Work towards achieving a healthy weight")

        return PredictionResult(
            condition="Heart Disease",
            risk_score=risk_score,
            risk_level=risk_level,
            confidence=88 + random.uniform(-8, 8),
            recommendations=recommendations,
            factors=risk_factors
        )

    def predict_general_health_risk(self, data: HealthData) -> PredictionResult:
        """Predict general health risk based on overall wellness indicators"""

        # Calculate BMI
        bmi = self.calculate_bmi(data.height, data.weight)

        # Risk factors with weights
        risk_factors = {
            'age': (data.age - 25) * 0.8 if data.age > 25 else 0,
            'bmi': abs(bmi - 22) * 2,  # Optimal BMI is around 22
            'blood_pressure': max((data.systolic_bp - 120) * 0.2, 0) + max((data.diastolic_bp - 80) * 0.3, 0),
            'cholesterol': max((data.cholesterol - 180) * 0.08, 0) if data.cholesterol > 180 else 0,
            'glucose': max((data.glucose - 90) * 0.3, 0) if data.glucose > 90 else 0,
            'smoking': 15 if data.smoking else 0,
            'exercise': max((4 - data.exercise_hours_per_week) * 2.5, 0),
            'stress': data.stress_level * 1.8,
            'family_history': 8 if data.family_history else 0
        }

        # Calculate total risk score
        base_risk = sum(risk_factors.values())
        risk_score = min(base_risk, 100)

        # Determine risk level
        if risk_score < 20:
            risk_level = "Low"
        elif risk_score < 40:
            risk_level = "Moderate"
        elif risk_score < 65:
            risk_level = "High"
        else:
            risk_level = "Very High"

        # Generate recommendations
        recommendations = []
        if bmi < 18.5:
            recommendations.append("Consider gaining weight through healthy nutrition")
        elif bmi > 25:
            recommendations.append("Focus on achieving a healthy weight")

        if data.exercise_hours_per_week < 3:
            recommendations.append("Increase physical activity for better overall health")
        if data.stress_level > 7:
            recommendations.append("Implement stress reduction strategies")
        if data.smoking:
            recommendations.append("Quit smoking to improve overall health")

        recommendations.append("Maintain regular health check-ups")
        recommendations.append("Follow a balanced, nutritious diet")

        return PredictionResult(
            condition="General Health Risk",
            risk_score=risk_score,
            risk_level=risk_level,
            confidence=82 + random.uniform(-7, 7),
            recommendations=recommendations,
            factors=risk_factors
        )

    def get_comprehensive_assessment(self, data: HealthData) -> Dict[str, PredictionResult]:
        """Get comprehensive health assessment for all conditions"""
        return {
            'diabetes': self.predict_diabetes_risk(data),
            'heart_disease': self.predict_heart_disease_risk(data),
            'general_health': self.predict_general_health_risk(data)
        }
